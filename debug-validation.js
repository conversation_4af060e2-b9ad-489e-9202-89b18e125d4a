// Quick debug script to test validation
import { getDeviceConfigFormValidationSchema } from './src/Pages/PlansPage/plans.utils.tsx';

const schema = getDeviceConfigFormValidationSchema();

// Test case: first row max=4, second row min=5 (should be VALID)
const testData = {
  items: [
    { min: 1, max: 4 },
    { min: 5, max: 10 }
  ]
};

console.log('Testing validation with data:', testData);

schema.validate(testData)
  .then(result => {
    console.log('✅ Validation PASSED:', result);
  })
  .catch(error => {
    console.log('❌ Validation FAILED:', error.message);
    console.log('Error path:', error.path);
  });
