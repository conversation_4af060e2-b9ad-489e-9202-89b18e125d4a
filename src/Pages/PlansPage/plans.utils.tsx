import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {Integers} from 'Helpers/integers';
import * as Yup from 'yup';
import {ActionButtons} from './PlanPage';

export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

export enum PlanStatus {
  ACTIVE,
  INACTIVE,
}

const whiteMain = 'white.main';

export const getStatusColor = (status: PlanStatus): string => {
  const statusColorMap: Record<number, string> = {
    [PlanStatus.ACTIVE]: `alert.success.bg`,
    [PlanStatus.INACTIVE]: `alert.error.bg`,
  };
  return statusColorMap[status] || whiteMain;
};

export enum PlanTierType {
  PREMIUM = 'premium',
  STANDARD = 'standard',
}

export interface DeviceConfigForm {
  min: string;
  max: string;
}

export interface DeviceConfigFormikValues {
  items: DeviceConfigForm[];
}

/**
 * Get the validation schema for the Device Config form
 * Implements complex validation rules:
 * 1. Overall form min=1, max=64
 * 2. Row-level: max > min for each row
 * 3. Sequential: next row min > previous row max
 * @returns Yup validation schema
 */
export const getDeviceConfigFormValidationSchema = () =>
  Yup.object({
    items: Yup.array()
      .of(
        Yup.object().shape({
          min: Yup.number()
            .min(Integers.One, 'Min value must be at least 1')
            .max(Integers.SixtyFour, 'Min value must be at most 64')
            .required('Min value is required')
            .typeError('Min value must be a number'),
          max: Yup.number()
            .min(Integers.One, 'Max value must be at least 1')
            .max(Integers.SixtyFour, 'Max value must be at most 64')
            .required('Max value is required')
            .typeError('Max value must be a number')
            .test('max-greater-than-min', 'Max value must be greater than Min value', function (value) {
              const {min} = this.parent;
              if (min && value) {
                return Number(value) > Number(min);
              }
              return true;
            }),
        }),
      )
      .test(
        'sequential-validation',
        'Invalid sequence: each row min must be greater than previous row max',
        function (items) {
          if (!items || items.length <= 1) return true;

          for (let i = 1; i < items.length; i++) {
            const currentMin = Number(items[i]?.min);
            const previousMax = Number(items[i - 1]?.max);

            if (currentMin && previousMax && currentMin <= previousMax) {
              return this.createError({
                path: `items[${i}].min`,
                message: `Min value must be greater than previous row's max value (${previousMax})`,
              });
            }
          }
          return true;
        },
      ),
  });

export const groupedFeatureOptions = [
  {
    label: 'Premium',
    value: PlanTierType.PREMIUM,
    features: ['Silo compute', 'Silo storage'],
  },
  {
    label: 'Standard',
    value: PlanTierType.STANDARD,
    features: ['Poled compute', 'Silo storage'],
  },
];

export const getFontColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: `alert.success.onBg`,
    [PlanStatus.INACTIVE]: `alert.error.onBg`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getIndicatorColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: 'alert.success.main',
    [PlanStatus.INACTIVE]: 'alert.error.main',
  };
  return statusColorMap[status] || whiteMain;
};

export const getStatusLabel = (status: PlanStatus | number): string => {
  const statusLabelMap: Record<PlanStatus | number, string> = {
    [PlanStatus.ACTIVE]: 'Active',
    [PlanStatus.INACTIVE]: 'Inactive',
  };
  return statusLabelMap[status] || '';
};

interface BillingCycle {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  cycleName: string;
  duration: number;
  durationUnit: string;
  description: string;
}

interface ICurrency {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

interface ConfigureDevice {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  min: string;
  max: string;
}

interface PlanTableRow {
  name: string;
  price: number;
  status: PlanStatus;
  configureDevice: ConfigureDevice;
  currency: ICurrency;
  billingCycle: BillingCycle;
  tier: string;
}

interface PlanTableColumn {
  header: string;
  accessorKey?: keyof PlanTableRow;
  id?: string;
  cell?: (context: CellContext<PlanTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  planName: 'name',
  status: 'status',
  configureDevice: 'configureDevice',
  currency: 'currency',
  billingCycle: 'billingCycle',
  tier: 'tier',
};

/**
 * Returns the corresponding backend column name for a given frontend column name.
 * If the column name does not exist in the mapping, returns the original column name.
 *
 * @param columnName - The frontend column name to map.
 * @returns The backend column name if found in the mapping; otherwise, the original column name.
 */
export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Capitalizes the first character of the given string and converts the rest to lowercase.
 *
 * @param str - The string to capitalize.
 * @returns The capitalized string.
 */
export const capitalize = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

/**
 * Defines the column configuration for the Plans table.
 *
 * Each column object specifies how the column should be rendered, including:
 * - `header`: The display name of the column.
 * - `accessorKey`: The key to access the corresponding value from the row data.
 * - `id`: A unique identifier for the column.
 * - `cell`: (Optional) A custom cell renderer function for displaying complex or formatted data.
 *
 * Columns include:
 * - Plan name
 * - Status (with custom status chip rendering)
 * - Number of devices (range or placeholder)
 * - Subscription Tenure (formatted with capitalization)
 * - Infra Configuration (formatted with capitalization)
 * - Price (formatted with currency symbol)
 * - Actions (custom action buttons)
 *
 * @type {PlanTableColumn[]}
 */
export const getPlanTableColumns = (refetchPlans: () => void): PlanTableColumn[] => [
  {
    header: 'Plan name',
    accessorKey: 'name',
    id: 'name',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => (
      <EllipsisText text={row.original.name && row.original.name.length > 0 ? row.original.name : '-'} />
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<PlanTableRow, unknown>) => {
      const status = context.getValue() as PlanStatus;
      const backgroundColor = getStatusColor(status);
      const color = getFontColor(status);
      const indicatorColor = getIndicatorColor(status);
      const label = getStatusLabel(status);
      return (
        <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
      );
    },
  },
  {
    header: 'No. of devices',
    accessorKey: 'configureDevice',
    id: 'configureDevice',
    cell: ({row}: CellContext<PlanTableRow, unknown>) =>
      row.original.configureDevice ? `${row.original.configureDevice.min} - ${row.original.configureDevice.max}` : '-',
  },
  {
    header: 'Subscription tenure',
    accessorKey: 'billingCycle',
    id: 'billingCycle',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => capitalize(row.original.billingCycle?.cycleName) ?? '-',
  },
  {
    header: 'Infra configuration',
    accessorKey: 'tier',
    id: 'tier',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => capitalize(row.original.tier) ?? '-',
  },
  {
    header: 'Price',
    accessorKey: 'price',
    id: 'price',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => {
      const {price} = row.original;
      const {currency} = row.original;
      return `${currency?.symbol ?? ''}${price}`;
    },
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<PlanTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} refetchPlans={refetchPlans} />
    ),
  },
];
