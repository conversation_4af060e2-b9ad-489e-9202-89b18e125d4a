import {Box, BoxProps} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {useFormikContext} from 'formik';
import {useNavigate} from 'react-router';
import {DeviceConfigForm} from './plans.utils';

export interface DeviceLimitActionsProps extends BoxProps {
  mode: 'view' | 'edit';
}

/**
 * Bottom action buttons for the Add User page
 * @param param - Props for the bottom action buttons
 * @returns JSX.Element
 */
const DeviceLimitActions: React.FC<DeviceLimitActionsProps> = ({mode, ...rest}) => {
  const {handleSubmit, isSubmitting, isValid, dirty, isValidating} = useFormikContext<{items: DeviceConfigForm[]}>();
  // const canSubmit = !isSubmitting && isValid && dirty && !isValidating;
  const canSubmit = true;
  const navigate = useNavigate();

  const getUpdateTitle = () => {
    if (isSubmitting) {
      return 'Updating...';
    }
    return 'Update';
  };

  const getAddUserTitle = () => {
    if (isSubmitting) {
      return 'Adding...';
    }
    return 'Add User(s)';
  };

  return (
    <Box {...rest} display="flex" justifyContent="flex-end" gap={2} sx={{mb: 1.5, mr: 1.5}}>
      <BorderButton
        data-testid="close-button"
        onClick={() => {
          navigate(-1);
        }}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
      >
        Cancel
      </BorderButton>
      <BlueButton
        data-testid="submit-button"
        loadingPosition="end"
        loading={isSubmitting}
        disabled={!canSubmit}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
        onClick={() => handleSubmit()}
      >
        Save Changes
      </BlueButton>
    </Box>
  );
};

export default DeviceLimitActions;
