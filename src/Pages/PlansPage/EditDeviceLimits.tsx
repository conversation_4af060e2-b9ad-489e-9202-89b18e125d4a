import {Box, BoxProps, Grid, Icon<PERSON>utton, Link, Typography} from '@mui/material';
import DeleteIcon from 'Assets/DeleteIcon';
import FormInput from 'Components/Forms/FormInput';
import {FieldArray, getIn, useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {StyleUtils} from 'Helpers/styleUtils';
import {FC} from 'react';
import {DeviceConfigForm} from './plans.utils';

interface FormikFieldArrayProps extends BoxProps {
  mode: 'edit' | 'view';
}

/**
 * Formik field array for the Add User page
 * @param param - Props for the Formik field array
 * @returns JSX.Element
 */
const EditDeviceLimitsFormikForm: FC<FormikFieldArrayProps> = ({mode, ...rest}) => {
  const {values, errors, touched} = useFormikContext<{items: DeviceConfigForm[]}>();

  const isReadOnly = mode === 'view';

  return (
    <Box {...rest}>
      <FieldArray name="items">
        {({push, remove}) => {
          const arraySize = values.items.length;
          const size: number = Integers.Six;
          const showDeleteButton = arraySize > 1 && !isReadOnly;
          const marginRight = showDeleteButton ? '3.5rem' : 0;
          return (
            <>
              {values.items.map((_, index) => (
                <Grid container spacing={2} sx={{mb: 2}} key={'id' + index}>
                  <Grid size={{xs: 12, md: size}}>
                    <Typography sx={StyleUtils.lalelStyles}>Min value*</Typography>
                    <FormInput
                      id={`items.${index}.min`}
                      name={`items.${index}.min`}
                      fullWidth
                      required
                      type="number"
                      sx={{...StyleUtils.inputBoxStyles, mr: marginRight}}
                      readOnly={isReadOnly}
                      disabled={isReadOnly}
                      errorMessage={
                        getIn(touched, `items.${index}.min`) && getIn(errors, `items.${index}.min`)
                          ? getIn(errors, `items.${index}.min`)
                          : ''
                      }
                      placeholder="Enter min value"
                    />
                  </Grid>
                  <Grid size={{xs: 12, md: size}}>
                    <Typography sx={StyleUtils.lalelStyles}>Max value*</Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      <Box sx={{flex: 1}}>
                        <FormInput
                          id={`items.${index}.max`}
                          name={`items.${index}.max`}
                          fullWidth
                          type="number"
                          required
                          sx={{...StyleUtils.inputBoxStyles, mr: marginRight}}
                          readOnly={isReadOnly}
                          disabled={isReadOnly}
                          errorMessage={
                            getIn(touched, `items.${index}.max`) && getIn(errors, `items.${index}.max`)
                              ? getIn(errors, `items.${index}.max`)
                              : ''
                          }
                          placeholder="Enter max value"
                        />
                      </Box>
                      {showDeleteButton && (
                        <IconButton sx={{width: '3.125rem', aspectRatio: 1}} onClick={() => remove(index)}>
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              ))}
              {errors.items && typeof errors.items === 'string' && (
                <Box sx={{mt: 2, display: 'flex', justifyContent: 'end', mr: marginRight, mb: 2}}>
                  <Typography color="error">{errors.items}</Typography>
                </Box>
              )}
              {mode === 'edit' && (
                <Box sx={{mb: 3, display: 'flex', justifyContent: 'flex-end', mr: marginRight}}>
                  <Link
                    component="button"
                    underline="hover"
                    variant="body2"
                    sx={{
                      color: 'secondary.main',
                      fontSize: 12,
                      fontWeight: 700,
                    }}
                    onClick={() => {
                      push({min: '', max: ''});
                    }}
                  >
                    + Add new limit
                  </Link>
                </Box>
              )}
            </>
          );
        }}
      </FieldArray>
    </Box>
  );
};
export default EditDeviceLimitsFormikForm;
