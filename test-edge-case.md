# Device Limits Validation - Edge Case Testing

## ✅ **Fixed Edge Case Scenario**

### **Problem Description**
When a user changes a max value in one row that becomes greater than or equal to the next row's min value, the form should:
1. Disable the submit button ✅
2. Show the error message on the **next row's min field** (not just disable the button) ✅

### **Test Scenarios**

#### **Scenario 1: User changes max value affecting next row**
```
Initial state:
Row 1: min=1, max=5
Row 2: min=6, max=10

User changes Row 1 max from 5 to 8:
Row 1: min=1, max=8
Row 2: min=6, max=10  ← Error should appear here: "Min value must be greater than previous row's max value (8)"
```

#### **Scenario 2: User changes max value to equal next row's min**
```
Initial state:
Row 1: min=1, max=5
Row 2: min=6, max=10

User changes Row 1 max from 5 to 6:
Row 1: min=1, max=6
Row 2: min=6, max=10  ← Error should appear here: "Min value must be greater than previous row's max value (6)"
```

#### **Scenario 3: Multiple rows affected**
```
Initial state:
Row 1: min=1, max=5
Row 2: min=6, max=10
Row 3: min=11, max=15

User changes Row 1 max from 5 to 12:
Row 1: min=1, max=12
Row 2: min=6, max=10   ← Error: "Min value must be greater than previous row's max value (12)"
Row 3: min=11, max=15  ← May also show error depending on Row 2's max
```

### **Implementation Details**

The validation schema now includes:
1. **Row-level validation**: Each min/max field validates against its own constraints
2. **Sequential validation**: Array-level test that checks all sequential relationships
3. **Bidirectional checking**: When a max value changes, it validates against the next row's min
4. **Proper error targeting**: Errors are displayed on the correct field (next row's min when max changes)

### **Testing Instructions**

1. Navigate to Device Limits page
2. Click Edit button
3. Add multiple rows using "+ Add new limit"
4. Enter valid sequential values (e.g., 1-5, 6-10, 11-15)
5. Change the max value of the first row to a higher value (e.g., 8)
6. Observe that:
   - Submit button becomes disabled
   - Error message appears on the second row's min field
   - Error message is specific and helpful

### **Validation Rules Summary**

✅ **Basic Rules**:
- Min/Max values must be between 1-64
- Max must be greater than Min in the same row

✅ **Sequential Rules**:
- Each row's min must be greater than the previous row's max
- When a max value changes, it validates against the next row's min

✅ **Submit Button**:
- Only enabled when form is valid, dirty, and not processing
- Disabled immediately when any validation rule fails

✅ **Real-time Feedback**:
- Validation runs on change and blur events
- Error messages appear on the correct fields
- Clear, specific error messages with actual values
